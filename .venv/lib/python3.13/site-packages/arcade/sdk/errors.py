from arcade.core.errors import Retry<PERSON><PERSON><PERSON><PERSON>rror, ToolExecutionError, ToolRuntimeError

__all__ = [
    "SDKError",
    "WeightError",
    "ToolRuntimeError",
    "ToolExecutionError",
    "RetryableToolError",
]


class SDKError(Exception):
    """Base class for all SDK errors."""


class WeightError(SDKError):
    """Raised when the critic weights do not abide by SDK weight constraints."""
