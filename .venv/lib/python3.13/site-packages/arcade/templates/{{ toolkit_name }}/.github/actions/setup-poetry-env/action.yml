name: "setup-poetry-env"{% raw %}
description: "Composite action to setup the Python and poetry environment."

inputs:
  python-version:
    required: false
    description: "The python version to use"
    default: "3.11"

runs:
  using: "composite"
  steps:
    - name: Set up python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 1.8.5
        virtualenvs-in-project: true

    - name: Generate poetry.lock
      run: poetry lock --no-update
      shell: bash

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v4
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ inputs.python-version }}-${{ hashFiles('poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --all-extras
      shell: bash
{% endraw %}
