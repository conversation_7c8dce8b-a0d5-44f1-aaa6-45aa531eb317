name: Publish to PyPI

on:
  release:
    types: [published]

jobs:
  pypi-publish:
    name: Publish to PyPi
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/project/{{ package_name }}
    permissions:
      id-token: write
    steps:
    - name: Check out
      uses: actions/checkout@v4

    - name: Set up python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: python -m build

    - name: Publish to PyPI
      env:
        TWINE_USERNAME: "__token__"{% raw %}
        TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
      run: twine upload dist/*
{% endraw %}
