[tool.poetry]
name = "{{ package_name }}"
version = "0.0.1"
description = "{{ toolkit_description }}"
authors = ["{{ toolkit_author_name }} <{{ toolkit_author_email }}>"]

[tool.poetry.dependencies]
python = "^3.10"
arcade-ai = "^1.0.5"

[tool.poetry.dev-dependencies]
pytest = "^8.3.0"
pytest-cov = "^4.0.0"
mypy = "^1.5.1"
pre-commit = "^3.4.0"
tox = "^4.11.1"
ruff = "^0.7.4"

[build-system]
requires = ["poetry-core>=1.0.0,<2.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
files = ["{{ package_name }}/**/*.py"]
python_version = "3.10"
disallow_untyped_defs = "True"
disallow_any_unimported = "True"
no_implicit_optional = "True"
check_untyped_defs = "True"
warn_return_any = "True"
warn_unused_ignores = "True"
show_error_codes = "True"
ignore_missing_imports = "True"

[tool.pytest.ini_options]
testpaths = ["tests"]

[tool.coverage.report]
skip_empty = true

