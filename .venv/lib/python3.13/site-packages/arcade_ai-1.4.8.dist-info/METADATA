Metadata-Version: 2.1
Name: arcade-ai
Version: 1.4.8
Summary: Arcade Python SDK and CLI
Author: Arcade
Author-email: <EMAIL>
Requires-Python: >=3.10,<4.0
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Provides-Extra: evals
Requires-Dist: Jinja2 (>=3.1.5,<4.0.0)
Requires-Dist: arcadepy (>=1.3.1,<2.0.0)
Requires-Dist: fastapi (>=0.115.3,<0.116.0)
Requires-Dist: loguru (>=0.7.0,<0.8.0)
Requires-Dist: numpy (>=2.0.0,<3.0.0) ; extra == "evals"
Requires-Dist: openai (>=1.36.0,<2.0.0)
Requires-Dist: opentelemetry-exporter-otlp-proto-common (==1.27.0)
Requires-Dist: opentelemetry-exporter-otlp-proto-http (==1.27.0)
Requires-Dist: opentelemetry-instrumentation-fastapi (==0.48b0)
Requires-Dist: packaging (>=24.1,<25.0)
Requires-Dist: pydantic (>=2.7.0,<3.0.0)
Requires-Dist: pyjwt (>=2.8.0,<3.0.0)
Requires-Dist: pyreadline3 (>=3.5.4,<4.0.0) ; sys_platform == "win32"
Requires-Dist: python-dateutil (>=2.8.2,<3.0.0) ; extra == "evals"
Requires-Dist: pytz (>=2024.1,<2025.0) ; extra == "evals"
Requires-Dist: pyyaml (>=6.0,<7.0)
Requires-Dist: rich (>=13.7.1,<14.0.0)
Requires-Dist: scikit-learn (>=1.5.0,<2.0.0) ; extra == "evals"
Requires-Dist: scipy (>=1.14.0,<2.0.0) ; extra == "evals"
Requires-Dist: toml (>=0.10.2,<0.11.0)
Requires-Dist: tqdm (>=4.1.0,<5.0.0)
Requires-Dist: typer (>=0.10.0)
Requires-Dist: types-python-dateutil (==2.9.0.20241003)
Requires-Dist: types-pytz (==2024.2.0.20241003)
Requires-Dist: types-toml (==0.10.8.20240310)
Requires-Dist: uvicorn (>=0.30.0,<0.31.0)
Requires-Dist: watchfiles (>=1.0.5,<2.0.0)
Description-Content-Type: text/markdown

# Arcade Python SDK and CLI

[Arcade](https://arcade.dev?ref=pypi) provides developer-focused tooling and APIs designed to improve the capabilities of LLM applications and agents.

By removing the complexity of connecting agentic applications with your users' data and services, Arcade enables developers to focus on building their agentic applications.

To learn more, check out our
- [Website](https://arcade.dev?ref=pypi)
- [GitHub](https://github.com/ArcadeAI/arcade-ai)
- [Documentation](https://docs.arcade.dev)
- [Discord](https://discord.com/invite/GUZEMpEZ9p)
- [X](https://x.com/TryArcade)
- [LinkedIn](https://www.linkedin.com/company/arcade-ai)

