# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: opentelemetry/proto/collector/logs/v1/logs_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from opentelemetry.proto.logs.v1 import logs_pb2 as opentelemetry_dot_proto_dot_logs_dot_v1_dot_logs__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8opentelemetry/proto/collector/logs/v1/logs_service.proto\x12%opentelemetry.proto.collector.logs.v1\x1a&opentelemetry/proto/logs/v1/logs.proto\"\\\n\x18\x45xportLogsServiceRequest\x12@\n\rresource_logs\x18\x01 \x03(\x0b\x32).opentelemetry.proto.logs.v1.ResourceLogs\"u\n\x19\x45xportLogsServiceResponse\x12X\n\x0fpartial_success\x18\x01 \x01(\x0b\x32?.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess\"O\n\x18\x45xportLogsPartialSuccess\x12\x1c\n\x14rejected_log_records\x18\x01 \x01(\x03\x12\x15\n\rerror_message\x18\x02 \x01(\t2\x9d\x01\n\x0bLogsService\x12\x8d\x01\n\x06\x45xport\x12?.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest\<EMAIL>\"\x00\x42\x98\x01\n(io.opentelemetry.proto.collector.logs.v1B\x10LogsServiceProtoP\x01Z0go.opentelemetry.io/proto/otlp/collector/logs/v1\xaa\x02%OpenTelemetry.Proto.Collector.Logs.V1b\x06proto3')



_EXPORTLOGSSERVICEREQUEST = DESCRIPTOR.message_types_by_name['ExportLogsServiceRequest']
_EXPORTLOGSSERVICERESPONSE = DESCRIPTOR.message_types_by_name['ExportLogsServiceResponse']
_EXPORTLOGSPARTIALSUCCESS = DESCRIPTOR.message_types_by_name['ExportLogsPartialSuccess']
ExportLogsServiceRequest = _reflection.GeneratedProtocolMessageType('ExportLogsServiceRequest', (_message.Message,), {
  'DESCRIPTOR' : _EXPORTLOGSSERVICEREQUEST,
  '__module__' : 'opentelemetry.proto.collector.logs.v1.logs_service_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest)
  })
_sym_db.RegisterMessage(ExportLogsServiceRequest)

ExportLogsServiceResponse = _reflection.GeneratedProtocolMessageType('ExportLogsServiceResponse', (_message.Message,), {
  'DESCRIPTOR' : _EXPORTLOGSSERVICERESPONSE,
  '__module__' : 'opentelemetry.proto.collector.logs.v1.logs_service_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse)
  })
_sym_db.RegisterMessage(ExportLogsServiceResponse)

ExportLogsPartialSuccess = _reflection.GeneratedProtocolMessageType('ExportLogsPartialSuccess', (_message.Message,), {
  'DESCRIPTOR' : _EXPORTLOGSPARTIALSUCCESS,
  '__module__' : 'opentelemetry.proto.collector.logs.v1.logs_service_pb2'
  # @@protoc_insertion_point(class_scope:opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess)
  })
_sym_db.RegisterMessage(ExportLogsPartialSuccess)

_LOGSSERVICE = DESCRIPTOR.services_by_name['LogsService']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n(io.opentelemetry.proto.collector.logs.v1B\020LogsServiceProtoP\001Z0go.opentelemetry.io/proto/otlp/collector/logs/v1\252\002%OpenTelemetry.Proto.Collector.Logs.V1'
  _EXPORTLOGSSERVICEREQUEST._serialized_start=139
  _EXPORTLOGSSERVICEREQUEST._serialized_end=231
  _EXPORTLOGSSERVICERESPONSE._serialized_start=233
  _EXPORTLOGSSERVICERESPONSE._serialized_end=350
  _EXPORTLOGSPARTIALSUCCESS._serialized_start=352
  _EXPORTLOGSPARTIALSUCCESS._serialized_end=431
  _LOGSSERVICE._serialized_start=434
  _LOGSSERVICE._serialized_end=591
# @@protoc_insertion_point(module_scope)
