"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import opentelemetry.proto.common.v1.common_pb2
import opentelemetry.proto.resource.v1.resource_pb2
import typing
import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor = ...

class SeverityNumber(_SeverityNumber, metaclass=_SeverityNumberEnumTypeWrapper):
    """Possible values for LogRecord.SeverityNumber."""
    pass
class _SeverityNumber:
    V = typing.NewType('V', builtins.int)
class _SeverityNumberEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SeverityNumber.V], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
    SEVERITY_NUMBER_UNSPECIFIED = SeverityNumber.V(0)
    """UNSPECIFIED is the default SeverityNumber, it MUST NOT be used."""

    SEVERITY_NUMBER_TRACE = SeverityNumber.V(1)
    SEVERITY_NUMBER_TRACE2 = SeverityNumber.V(2)
    SEVERITY_NUMBER_TRACE3 = SeverityNumber.V(3)
    SEVERITY_NUMBER_TRACE4 = SeverityNumber.V(4)
    SEVERITY_NUMBER_DEBUG = SeverityNumber.V(5)
    SEVERITY_NUMBER_DEBUG2 = SeverityNumber.V(6)
    SEVERITY_NUMBER_DEBUG3 = SeverityNumber.V(7)
    SEVERITY_NUMBER_DEBUG4 = SeverityNumber.V(8)
    SEVERITY_NUMBER_INFO = SeverityNumber.V(9)
    SEVERITY_NUMBER_INFO2 = SeverityNumber.V(10)
    SEVERITY_NUMBER_INFO3 = SeverityNumber.V(11)
    SEVERITY_NUMBER_INFO4 = SeverityNumber.V(12)
    SEVERITY_NUMBER_WARN = SeverityNumber.V(13)
    SEVERITY_NUMBER_WARN2 = SeverityNumber.V(14)
    SEVERITY_NUMBER_WARN3 = SeverityNumber.V(15)
    SEVERITY_NUMBER_WARN4 = SeverityNumber.V(16)
    SEVERITY_NUMBER_ERROR = SeverityNumber.V(17)
    SEVERITY_NUMBER_ERROR2 = SeverityNumber.V(18)
    SEVERITY_NUMBER_ERROR3 = SeverityNumber.V(19)
    SEVERITY_NUMBER_ERROR4 = SeverityNumber.V(20)
    SEVERITY_NUMBER_FATAL = SeverityNumber.V(21)
    SEVERITY_NUMBER_FATAL2 = SeverityNumber.V(22)
    SEVERITY_NUMBER_FATAL3 = SeverityNumber.V(23)
    SEVERITY_NUMBER_FATAL4 = SeverityNumber.V(24)

SEVERITY_NUMBER_UNSPECIFIED = SeverityNumber.V(0)
"""UNSPECIFIED is the default SeverityNumber, it MUST NOT be used."""

SEVERITY_NUMBER_TRACE = SeverityNumber.V(1)
SEVERITY_NUMBER_TRACE2 = SeverityNumber.V(2)
SEVERITY_NUMBER_TRACE3 = SeverityNumber.V(3)
SEVERITY_NUMBER_TRACE4 = SeverityNumber.V(4)
SEVERITY_NUMBER_DEBUG = SeverityNumber.V(5)
SEVERITY_NUMBER_DEBUG2 = SeverityNumber.V(6)
SEVERITY_NUMBER_DEBUG3 = SeverityNumber.V(7)
SEVERITY_NUMBER_DEBUG4 = SeverityNumber.V(8)
SEVERITY_NUMBER_INFO = SeverityNumber.V(9)
SEVERITY_NUMBER_INFO2 = SeverityNumber.V(10)
SEVERITY_NUMBER_INFO3 = SeverityNumber.V(11)
SEVERITY_NUMBER_INFO4 = SeverityNumber.V(12)
SEVERITY_NUMBER_WARN = SeverityNumber.V(13)
SEVERITY_NUMBER_WARN2 = SeverityNumber.V(14)
SEVERITY_NUMBER_WARN3 = SeverityNumber.V(15)
SEVERITY_NUMBER_WARN4 = SeverityNumber.V(16)
SEVERITY_NUMBER_ERROR = SeverityNumber.V(17)
SEVERITY_NUMBER_ERROR2 = SeverityNumber.V(18)
SEVERITY_NUMBER_ERROR3 = SeverityNumber.V(19)
SEVERITY_NUMBER_ERROR4 = SeverityNumber.V(20)
SEVERITY_NUMBER_FATAL = SeverityNumber.V(21)
SEVERITY_NUMBER_FATAL2 = SeverityNumber.V(22)
SEVERITY_NUMBER_FATAL3 = SeverityNumber.V(23)
SEVERITY_NUMBER_FATAL4 = SeverityNumber.V(24)
global___SeverityNumber = SeverityNumber


class LogRecordFlags(_LogRecordFlags, metaclass=_LogRecordFlagsEnumTypeWrapper):
    """LogRecordFlags represents constants used to interpret the
    LogRecord.flags field, which is protobuf 'fixed32' type and is to
    be used as bit-fields. Each non-zero value defined in this enum is
    a bit-mask.  To extract the bit-field, for example, use an
    expression like:

      (logRecord.flags & LOG_RECORD_FLAGS_TRACE_FLAGS_MASK)
    """
    pass
class _LogRecordFlags:
    V = typing.NewType('V', builtins.int)
class _LogRecordFlagsEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_LogRecordFlags.V], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
    LOG_RECORD_FLAGS_DO_NOT_USE = LogRecordFlags.V(0)
    """The zero value for the enum. Should not be used for comparisons.
    Instead use bitwise "and" with the appropriate mask as shown above.
    """

    LOG_RECORD_FLAGS_TRACE_FLAGS_MASK = LogRecordFlags.V(255)
    """Bits 0-7 are used for trace flags."""


LOG_RECORD_FLAGS_DO_NOT_USE = LogRecordFlags.V(0)
"""The zero value for the enum. Should not be used for comparisons.
Instead use bitwise "and" with the appropriate mask as shown above.
"""

LOG_RECORD_FLAGS_TRACE_FLAGS_MASK = LogRecordFlags.V(255)
"""Bits 0-7 are used for trace flags."""

global___LogRecordFlags = LogRecordFlags


class LogsData(google.protobuf.message.Message):
    """LogsData represents the logs data that can be stored in a persistent storage,
    OR can be embedded by other protocols that transfer OTLP logs data but do not
    implement the OTLP protocol.

    The main difference between this message and collector protocol is that
    in this message there will not be any "control" or "metadata" specific to
    OTLP protocol.

    When new fields are added into this message, the OTLP request MUST be updated
    as well.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    RESOURCE_LOGS_FIELD_NUMBER: builtins.int
    @property
    def resource_logs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ResourceLogs]:
        """An array of ResourceLogs.
        For data coming from a single resource this array will typically contain
        one element. Intermediary nodes that receive data from multiple origins
        typically batch the data before forwarding further and in that case this
        array will contain multiple elements.
        """
        pass
    def __init__(self,
        *,
        resource_logs : typing.Optional[typing.Iterable[global___ResourceLogs]] = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["resource_logs",b"resource_logs"]) -> None: ...
global___LogsData = LogsData

class ResourceLogs(google.protobuf.message.Message):
    """A collection of ScopeLogs from a Resource."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    RESOURCE_FIELD_NUMBER: builtins.int
    SCOPE_LOGS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def resource(self) -> opentelemetry.proto.resource.v1.resource_pb2.Resource:
        """The resource for the logs in this message.
        If this field is not set then resource info is unknown.
        """
        pass
    @property
    def scope_logs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ScopeLogs]:
        """A list of ScopeLogs that originate from a resource."""
        pass
    schema_url: typing.Text = ...
    """The Schema URL, if known. This is the identifier of the Schema that the resource data
    is recorded in. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to the data in the "resource" field. It does not apply
    to the data in the "scope_logs" field which have their own schema_url field.
    """

    def __init__(self,
        *,
        resource : typing.Optional[opentelemetry.proto.resource.v1.resource_pb2.Resource] = ...,
        scope_logs : typing.Optional[typing.Iterable[global___ScopeLogs]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["resource",b"resource"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["resource",b"resource","schema_url",b"schema_url","scope_logs",b"scope_logs"]) -> None: ...
global___ResourceLogs = ResourceLogs

class ScopeLogs(google.protobuf.message.Message):
    """A collection of Logs produced by a Scope."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    SCOPE_FIELD_NUMBER: builtins.int
    LOG_RECORDS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def scope(self) -> opentelemetry.proto.common.v1.common_pb2.InstrumentationScope:
        """The instrumentation scope information for the logs in this message.
        Semantically when InstrumentationScope isn't set, it is equivalent with
        an empty instrumentation scope name (unknown).
        """
        pass
    @property
    def log_records(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LogRecord]:
        """A list of log records."""
        pass
    schema_url: typing.Text = ...
    """The Schema URL, if known. This is the identifier of the Schema that the log data
    is recorded in. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to all logs in the "logs" field.
    """

    def __init__(self,
        *,
        scope : typing.Optional[opentelemetry.proto.common.v1.common_pb2.InstrumentationScope] = ...,
        log_records : typing.Optional[typing.Iterable[global___LogRecord]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["scope",b"scope"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["log_records",b"log_records","schema_url",b"schema_url","scope",b"scope"]) -> None: ...
global___ScopeLogs = ScopeLogs

class LogRecord(google.protobuf.message.Message):
    """A log record according to OpenTelemetry Log Data Model:
    https://github.com/open-telemetry/oteps/blob/main/text/logs/0097-log-data-model.md
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    OBSERVED_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    SEVERITY_NUMBER_FIELD_NUMBER: builtins.int
    SEVERITY_TEXT_FIELD_NUMBER: builtins.int
    BODY_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    TRACE_ID_FIELD_NUMBER: builtins.int
    SPAN_ID_FIELD_NUMBER: builtins.int
    time_unix_nano: builtins.int = ...
    """time_unix_nano is the time when the event occurred.
    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.
    Value of 0 indicates unknown or missing timestamp.
    """

    observed_time_unix_nano: builtins.int = ...
    """Time when the event was observed by the collection system.
    For events that originate in OpenTelemetry (e.g. using OpenTelemetry Logging SDK)
    this timestamp is typically set at the generation time and is equal to Timestamp.
    For events originating externally and collected by OpenTelemetry (e.g. using
    Collector) this is the time when OpenTelemetry's code observed the event measured
    by the clock of the OpenTelemetry code. This field MUST be set once the event is
    observed by OpenTelemetry.

    For converting OpenTelemetry log data to formats that support only one timestamp or
    when receiving OpenTelemetry log data by recipients that support only one timestamp
    internally the following logic is recommended:
      - Use time_unix_nano if it is present, otherwise use observed_time_unix_nano.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.
    Value of 0 indicates unknown or missing timestamp.
    """

    severity_number: global___SeverityNumber.V = ...
    """Numerical value of the severity, normalized to values described in Log Data Model.
    [Optional].
    """

    severity_text: typing.Text = ...
    """The severity text (also known as log level). The original string representation as
    it is known at the source. [Optional].
    """

    @property
    def body(self) -> opentelemetry.proto.common.v1.common_pb2.AnyValue:
        """A value containing the body of the log record. Can be for example a human-readable
        string message (including multi-line) describing the event in a free form or it can
        be a structured data composed of arrays and maps of other values. [Optional].
        """
        pass
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """Additional attributes that describe the specific event occurrence. [Optional].
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    dropped_attributes_count: builtins.int = ...
    flags: builtins.int = ...
    """Flags, a bit field. 8 least significant bits are the trace flags as
    defined in W3C Trace Context specification. 24 most significant bits are reserved
    and must be set to 0. Readers must not assume that 24 most significant bits
    will be zero and must correctly mask the bits when reading 8-bit trace flag (use
    flags & LOG_RECORD_FLAGS_TRACE_FLAGS_MASK). [Optional].
    """

    trace_id: builtins.bytes = ...
    """A unique identifier for a trace. All logs from the same trace share
    the same `trace_id`. The ID is a 16-byte array. An ID with all zeroes OR
    of length other than 16 bytes is considered invalid (empty string in OTLP/JSON
    is zero-length and thus is also invalid).

    This field is optional.

    The receivers SHOULD assume that the log record is not associated with a
    trace if any of the following is true:
      - the field is not present,
      - the field contains an invalid value.
    """

    span_id: builtins.bytes = ...
    """A unique identifier for a span within a trace, assigned when the span
    is created. The ID is an 8-byte array. An ID with all zeroes OR of length
    other than 8 bytes is considered invalid (empty string in OTLP/JSON
    is zero-length and thus is also invalid).

    This field is optional. If the sender specifies a valid span_id then it SHOULD also
    specify a valid trace_id.

    The receivers SHOULD assume that the log record is not associated with a
    span if any of the following is true:
      - the field is not present,
      - the field contains an invalid value.
    """

    def __init__(self,
        *,
        time_unix_nano : builtins.int = ...,
        observed_time_unix_nano : builtins.int = ...,
        severity_number : global___SeverityNumber.V = ...,
        severity_text : typing.Text = ...,
        body : typing.Optional[opentelemetry.proto.common.v1.common_pb2.AnyValue] = ...,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        dropped_attributes_count : builtins.int = ...,
        flags : builtins.int = ...,
        trace_id : builtins.bytes = ...,
        span_id : builtins.bytes = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["body",b"body"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["attributes",b"attributes","body",b"body","dropped_attributes_count",b"dropped_attributes_count","flags",b"flags","observed_time_unix_nano",b"observed_time_unix_nano","severity_number",b"severity_number","severity_text",b"severity_text","span_id",b"span_id","time_unix_nano",b"time_unix_nano","trace_id",b"trace_id"]) -> None: ...
global___LogRecord = LogRecord
