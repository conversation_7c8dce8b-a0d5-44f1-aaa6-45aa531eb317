"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import opentelemetry.proto.common.v1.common_pb2
import opentelemetry.proto.resource.v1.resource_pb2
import typing
import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor = ...

class AggregationTemporality(_AggregationTemporality, metaclass=_AggregationTemporalityEnumTypeWrapper):
    """AggregationTemporality defines how a metric aggregator reports aggregated
    values. It describes how those values relate to the time interval over
    which they are aggregated.
    """
    pass
class _AggregationTemporality:
    V = typing.NewType('V', builtins.int)
class _AggregationTemporalityEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AggregationTemporality.V], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
    AGGREGATION_TEMPORALITY_UNSPECIFIED = AggregationTemporality.V(0)
    """UNSPECIFIED is the default AggregationTemporality, it MUST not be used."""

    AGGREGATION_TEMPORALITY_DELTA = AggregationTemporality.V(1)
    """DELTA is an AggregationTemporality for a metric aggregator which reports
    changes since last report time. Successive metrics contain aggregation of
    values from continuous and non-overlapping intervals.

    The values for a DELTA metric are based only on the time interval
    associated with one measurement cycle. There is no dependency on
    previous measurements like is the case for CUMULATIVE metrics.

    For example, consider a system measuring the number of requests that
    it receives and reports the sum of these requests every second as a
    DELTA metric:

      1. The system starts receiving at time=t_0.
      2. A request is received, the system measures 1 request.
      3. A request is received, the system measures 1 request.
      4. A request is received, the system measures 1 request.
      5. The 1 second collection cycle ends. A metric is exported for the
         number of requests received over the interval of time t_0 to
         t_0+1 with a value of 3.
      6. A request is received, the system measures 1 request.
      7. A request is received, the system measures 1 request.
      8. The 1 second collection cycle ends. A metric is exported for the
         number of requests received over the interval of time t_0+1 to
         t_0+2 with a value of 2.
    """

    AGGREGATION_TEMPORALITY_CUMULATIVE = AggregationTemporality.V(2)
    """CUMULATIVE is an AggregationTemporality for a metric aggregator which
    reports changes since a fixed start time. This means that current values
    of a CUMULATIVE metric depend on all previous measurements since the
    start time. Because of this, the sender is required to retain this state
    in some form. If this state is lost or invalidated, the CUMULATIVE metric
    values MUST be reset and a new fixed start time following the last
    reported measurement time sent MUST be used.

    For example, consider a system measuring the number of requests that
    it receives and reports the sum of these requests every second as a
    CUMULATIVE metric:

      1. The system starts receiving at time=t_0.
      2. A request is received, the system measures 1 request.
      3. A request is received, the system measures 1 request.
      4. A request is received, the system measures 1 request.
      5. The 1 second collection cycle ends. A metric is exported for the
         number of requests received over the interval of time t_0 to
         t_0+1 with a value of 3.
      6. A request is received, the system measures 1 request.
      7. A request is received, the system measures 1 request.
      8. The 1 second collection cycle ends. A metric is exported for the
         number of requests received over the interval of time t_0 to
         t_0+2 with a value of 5.
      9. The system experiences a fault and loses state.
      10. The system recovers and resumes receiving at time=t_1.
      11. A request is received, the system measures 1 request.
      12. The 1 second collection cycle ends. A metric is exported for the
         number of requests received over the interval of time t_1 to
         t_0+1 with a value of 1.

    Note: Even though, when reporting changes since last report time, using
    CUMULATIVE is valid, it is not recommended. This may cause problems for
    systems that do not use start_time to determine when the aggregation
    value was reset (e.g. Prometheus).
    """


AGGREGATION_TEMPORALITY_UNSPECIFIED = AggregationTemporality.V(0)
"""UNSPECIFIED is the default AggregationTemporality, it MUST not be used."""

AGGREGATION_TEMPORALITY_DELTA = AggregationTemporality.V(1)
"""DELTA is an AggregationTemporality for a metric aggregator which reports
changes since last report time. Successive metrics contain aggregation of
values from continuous and non-overlapping intervals.

The values for a DELTA metric are based only on the time interval
associated with one measurement cycle. There is no dependency on
previous measurements like is the case for CUMULATIVE metrics.

For example, consider a system measuring the number of requests that
it receives and reports the sum of these requests every second as a
DELTA metric:

  1. The system starts receiving at time=t_0.
  2. A request is received, the system measures 1 request.
  3. A request is received, the system measures 1 request.
  4. A request is received, the system measures 1 request.
  5. The 1 second collection cycle ends. A metric is exported for the
     number of requests received over the interval of time t_0 to
     t_0+1 with a value of 3.
  6. A request is received, the system measures 1 request.
  7. A request is received, the system measures 1 request.
  8. The 1 second collection cycle ends. A metric is exported for the
     number of requests received over the interval of time t_0+1 to
     t_0+2 with a value of 2.
"""

AGGREGATION_TEMPORALITY_CUMULATIVE = AggregationTemporality.V(2)
"""CUMULATIVE is an AggregationTemporality for a metric aggregator which
reports changes since a fixed start time. This means that current values
of a CUMULATIVE metric depend on all previous measurements since the
start time. Because of this, the sender is required to retain this state
in some form. If this state is lost or invalidated, the CUMULATIVE metric
values MUST be reset and a new fixed start time following the last
reported measurement time sent MUST be used.

For example, consider a system measuring the number of requests that
it receives and reports the sum of these requests every second as a
CUMULATIVE metric:

  1. The system starts receiving at time=t_0.
  2. A request is received, the system measures 1 request.
  3. A request is received, the system measures 1 request.
  4. A request is received, the system measures 1 request.
  5. The 1 second collection cycle ends. A metric is exported for the
     number of requests received over the interval of time t_0 to
     t_0+1 with a value of 3.
  6. A request is received, the system measures 1 request.
  7. A request is received, the system measures 1 request.
  8. The 1 second collection cycle ends. A metric is exported for the
     number of requests received over the interval of time t_0 to
     t_0+2 with a value of 5.
  9. The system experiences a fault and loses state.
  10. The system recovers and resumes receiving at time=t_1.
  11. A request is received, the system measures 1 request.
  12. The 1 second collection cycle ends. A metric is exported for the
     number of requests received over the interval of time t_1 to
     t_0+1 with a value of 1.

Note: Even though, when reporting changes since last report time, using
CUMULATIVE is valid, it is not recommended. This may cause problems for
systems that do not use start_time to determine when the aggregation
value was reset (e.g. Prometheus).
"""

global___AggregationTemporality = AggregationTemporality


class DataPointFlags(_DataPointFlags, metaclass=_DataPointFlagsEnumTypeWrapper):
    """DataPointFlags is defined as a protobuf 'uint32' type and is to be used as a
    bit-field representing 32 distinct boolean flags.  Each flag defined in this
    enum is a bit-mask.  To test the presence of a single flag in the flags of
    a data point, for example, use an expression like:

      (point.flags & DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK) == DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK
    """
    pass
class _DataPointFlags:
    V = typing.NewType('V', builtins.int)
class _DataPointFlagsEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DataPointFlags.V], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor = ...
    DATA_POINT_FLAGS_DO_NOT_USE = DataPointFlags.V(0)
    """The zero value for the enum. Should not be used for comparisons.
    Instead use bitwise "and" with the appropriate mask as shown above.
    """

    DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK = DataPointFlags.V(1)
    """This DataPoint is valid but has no recorded value.  This value
    SHOULD be used to reflect explicitly missing data in a series, as
    for an equivalent to the Prometheus "staleness marker".
    """


DATA_POINT_FLAGS_DO_NOT_USE = DataPointFlags.V(0)
"""The zero value for the enum. Should not be used for comparisons.
Instead use bitwise "and" with the appropriate mask as shown above.
"""

DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK = DataPointFlags.V(1)
"""This DataPoint is valid but has no recorded value.  This value
SHOULD be used to reflect explicitly missing data in a series, as
for an equivalent to the Prometheus "staleness marker".
"""

global___DataPointFlags = DataPointFlags


class MetricsData(google.protobuf.message.Message):
    """MetricsData represents the metrics data that can be stored in a persistent
    storage, OR can be embedded by other protocols that transfer OTLP metrics
    data but do not implement the OTLP protocol.

    The main difference between this message and collector protocol is that
    in this message there will not be any "control" or "metadata" specific to
    OTLP protocol.

    When new fields are added into this message, the OTLP request MUST be updated
    as well.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    RESOURCE_METRICS_FIELD_NUMBER: builtins.int
    @property
    def resource_metrics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ResourceMetrics]:
        """An array of ResourceMetrics.
        For data coming from a single resource this array will typically contain
        one element. Intermediary nodes that receive data from multiple origins
        typically batch the data before forwarding further and in that case this
        array will contain multiple elements.
        """
        pass
    def __init__(self,
        *,
        resource_metrics : typing.Optional[typing.Iterable[global___ResourceMetrics]] = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["resource_metrics",b"resource_metrics"]) -> None: ...
global___MetricsData = MetricsData

class ResourceMetrics(google.protobuf.message.Message):
    """A collection of ScopeMetrics from a Resource."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    RESOURCE_FIELD_NUMBER: builtins.int
    SCOPE_METRICS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def resource(self) -> opentelemetry.proto.resource.v1.resource_pb2.Resource:
        """The resource for the metrics in this message.
        If this field is not set then no resource info is known.
        """
        pass
    @property
    def scope_metrics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ScopeMetrics]:
        """A list of metrics that originate from a resource."""
        pass
    schema_url: typing.Text = ...
    """The Schema URL, if known. This is the identifier of the Schema that the resource data
    is recorded in. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to the data in the "resource" field. It does not apply
    to the data in the "scope_metrics" field which have their own schema_url field.
    """

    def __init__(self,
        *,
        resource : typing.Optional[opentelemetry.proto.resource.v1.resource_pb2.Resource] = ...,
        scope_metrics : typing.Optional[typing.Iterable[global___ScopeMetrics]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["resource",b"resource"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["resource",b"resource","schema_url",b"schema_url","scope_metrics",b"scope_metrics"]) -> None: ...
global___ResourceMetrics = ResourceMetrics

class ScopeMetrics(google.protobuf.message.Message):
    """A collection of Metrics produced by an Scope."""
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    SCOPE_FIELD_NUMBER: builtins.int
    METRICS_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def scope(self) -> opentelemetry.proto.common.v1.common_pb2.InstrumentationScope:
        """The instrumentation scope information for the metrics in this message.
        Semantically when InstrumentationScope isn't set, it is equivalent with
        an empty instrumentation scope name (unknown).
        """
        pass
    @property
    def metrics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Metric]:
        """A list of metrics that originate from an instrumentation library."""
        pass
    schema_url: typing.Text = ...
    """The Schema URL, if known. This is the identifier of the Schema that the metric data
    is recorded in. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to all metrics in the "metrics" field.
    """

    def __init__(self,
        *,
        scope : typing.Optional[opentelemetry.proto.common.v1.common_pb2.InstrumentationScope] = ...,
        metrics : typing.Optional[typing.Iterable[global___Metric]] = ...,
        schema_url : typing.Text = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["scope",b"scope"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["metrics",b"metrics","schema_url",b"schema_url","scope",b"scope"]) -> None: ...
global___ScopeMetrics = ScopeMetrics

class Metric(google.protobuf.message.Message):
    """Defines a Metric which has one or more timeseries.  The following is a
    brief summary of the Metric data model.  For more details, see:

      https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/data-model.md


    The data model and relation between entities is shown in the
    diagram below. Here, "DataPoint" is the term used to refer to any
    one of the specific data point value types, and "points" is the term used
    to refer to any one of the lists of points contained in the Metric.

    - Metric is composed of a metadata and data.
    - Metadata part contains a name, description, unit.
    - Data is one of the possible types (Sum, Gauge, Histogram, Summary).
    - DataPoint contains timestamps, attributes, and one of the possible value type
      fields.

        Metric
     +------------+
     |name        |
     |description |
     |unit        |     +------------------------------------+
     |data        |---> |Gauge, Sum, Histogram, Summary, ... |
     +------------+     +------------------------------------+

       Data [One of Gauge, Sum, Histogram, Summary, ...]
     +-----------+
     |...        |  // Metadata about the Data.
     |points     |--+
     +-----------+  |
                    |      +---------------------------+
                    |      |DataPoint 1                |
                    v      |+------+------+   +------+ |
                 +-----+   ||label |label |...|label | |
                 |  1  |-->||value1|value2|...|valueN| |
                 +-----+   |+------+------+   +------+ |
                 |  .  |   |+-----+                    |
                 |  .  |   ||value|                    |
                 |  .  |   |+-----+                    |
                 |  .  |   +---------------------------+
                 |  .  |                   .
                 |  .  |                   .
                 |  .  |                   .
                 |  .  |   +---------------------------+
                 |  .  |   |DataPoint M                |
                 +-----+   |+------+------+   +------+ |
                 |  M  |-->||label |label |...|label | |
                 +-----+   ||value1|value2|...|valueN| |
                           |+------+------+   +------+ |
                           |+-----+                    |
                           ||value|                    |
                           |+-----+                    |
                           +---------------------------+

    Each distinct type of DataPoint represents the output of a specific
    aggregation function, the result of applying the DataPoint's
    associated function of to one or more measurements.

    All DataPoint types have three common fields:
    - Attributes includes key-value pairs associated with the data point
    - TimeUnixNano is required, set to the end time of the aggregation
    - StartTimeUnixNano is optional, but strongly encouraged for DataPoints
      having an AggregationTemporality field, as discussed below.

    Both TimeUnixNano and StartTimeUnixNano values are expressed as
    UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970.

    # TimeUnixNano

    This field is required, having consistent interpretation across
    DataPoint types.  TimeUnixNano is the moment corresponding to when
    the data point's aggregate value was captured.

    Data points with the 0 value for TimeUnixNano SHOULD be rejected
    by consumers.

    # StartTimeUnixNano

    StartTimeUnixNano in general allows detecting when a sequence of
    observations is unbroken.  This field indicates to consumers the
    start time for points with cumulative and delta
    AggregationTemporality, and it should be included whenever possible
    to support correct rate calculation.  Although it may be omitted
    when the start time is truly unknown, setting StartTimeUnixNano is
    strongly encouraged.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    NAME_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    UNIT_FIELD_NUMBER: builtins.int
    GAUGE_FIELD_NUMBER: builtins.int
    SUM_FIELD_NUMBER: builtins.int
    HISTOGRAM_FIELD_NUMBER: builtins.int
    EXPONENTIAL_HISTOGRAM_FIELD_NUMBER: builtins.int
    SUMMARY_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    name: typing.Text = ...
    """name of the metric."""

    description: typing.Text = ...
    """description of the metric, which can be used in documentation."""

    unit: typing.Text = ...
    """unit in which the metric value is reported. Follows the format
    described by http://unitsofmeasure.org/ucum.html.
    """

    @property
    def gauge(self) -> global___Gauge: ...
    @property
    def sum(self) -> global___Sum: ...
    @property
    def histogram(self) -> global___Histogram: ...
    @property
    def exponential_histogram(self) -> global___ExponentialHistogram: ...
    @property
    def summary(self) -> global___Summary: ...
    @property
    def metadata(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """Additional metadata attributes that describe the metric. [Optional].
        Attributes are non-identifying.
        Consumers SHOULD NOT need to be aware of these attributes.
        These attributes MAY be used to encode information allowing
        for lossless roundtrip translation to / from another data model.
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    def __init__(self,
        *,
        name : typing.Text = ...,
        description : typing.Text = ...,
        unit : typing.Text = ...,
        gauge : typing.Optional[global___Gauge] = ...,
        sum : typing.Optional[global___Sum] = ...,
        histogram : typing.Optional[global___Histogram] = ...,
        exponential_histogram : typing.Optional[global___ExponentialHistogram] = ...,
        summary : typing.Optional[global___Summary] = ...,
        metadata : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data",b"data","exponential_histogram",b"exponential_histogram","gauge",b"gauge","histogram",b"histogram","sum",b"sum","summary",b"summary"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data",b"data","description",b"description","exponential_histogram",b"exponential_histogram","gauge",b"gauge","histogram",b"histogram","metadata",b"metadata","name",b"name","sum",b"sum","summary",b"summary","unit",b"unit"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data",b"data"]) -> typing.Optional[typing_extensions.Literal["gauge","sum","histogram","exponential_histogram","summary"]]: ...
global___Metric = Metric

class Gauge(google.protobuf.message.Message):
    """Gauge represents the type of a scalar metric that always exports the
    "current value" for every data point. It should be used for an "unknown"
    aggregation.

    A Gauge does not support different aggregation temporalities. Given the
    aggregation is unknown, points cannot be combined using the same
    aggregation, regardless of aggregation temporalities. Therefore,
    AggregationTemporality is not included. Consequently, this also means
    "StartTimeUnixNano" is ignored for all data points.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    DATA_POINTS_FIELD_NUMBER: builtins.int
    @property
    def data_points(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NumberDataPoint]: ...
    def __init__(self,
        *,
        data_points : typing.Optional[typing.Iterable[global___NumberDataPoint]] = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data_points",b"data_points"]) -> None: ...
global___Gauge = Gauge

class Sum(google.protobuf.message.Message):
    """Sum represents the type of a scalar metric that is calculated as a sum of all
    reported measurements over a time interval.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    DATA_POINTS_FIELD_NUMBER: builtins.int
    AGGREGATION_TEMPORALITY_FIELD_NUMBER: builtins.int
    IS_MONOTONIC_FIELD_NUMBER: builtins.int
    @property
    def data_points(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NumberDataPoint]: ...
    aggregation_temporality: global___AggregationTemporality.V = ...
    """aggregation_temporality describes if the aggregator reports delta changes
    since last report time, or cumulative changes since a fixed start time.
    """

    is_monotonic: builtins.bool = ...
    """If "true" means that the sum is monotonic."""

    def __init__(self,
        *,
        data_points : typing.Optional[typing.Iterable[global___NumberDataPoint]] = ...,
        aggregation_temporality : global___AggregationTemporality.V = ...,
        is_monotonic : builtins.bool = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["aggregation_temporality",b"aggregation_temporality","data_points",b"data_points","is_monotonic",b"is_monotonic"]) -> None: ...
global___Sum = Sum

class Histogram(google.protobuf.message.Message):
    """Histogram represents the type of a metric that is calculated by aggregating
    as a Histogram of all reported measurements over a time interval.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    DATA_POINTS_FIELD_NUMBER: builtins.int
    AGGREGATION_TEMPORALITY_FIELD_NUMBER: builtins.int
    @property
    def data_points(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___HistogramDataPoint]: ...
    aggregation_temporality: global___AggregationTemporality.V = ...
    """aggregation_temporality describes if the aggregator reports delta changes
    since last report time, or cumulative changes since a fixed start time.
    """

    def __init__(self,
        *,
        data_points : typing.Optional[typing.Iterable[global___HistogramDataPoint]] = ...,
        aggregation_temporality : global___AggregationTemporality.V = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["aggregation_temporality",b"aggregation_temporality","data_points",b"data_points"]) -> None: ...
global___Histogram = Histogram

class ExponentialHistogram(google.protobuf.message.Message):
    """ExponentialHistogram represents the type of a metric that is calculated by aggregating
    as a ExponentialHistogram of all reported double measurements over a time interval.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    DATA_POINTS_FIELD_NUMBER: builtins.int
    AGGREGATION_TEMPORALITY_FIELD_NUMBER: builtins.int
    @property
    def data_points(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ExponentialHistogramDataPoint]: ...
    aggregation_temporality: global___AggregationTemporality.V = ...
    """aggregation_temporality describes if the aggregator reports delta changes
    since last report time, or cumulative changes since a fixed start time.
    """

    def __init__(self,
        *,
        data_points : typing.Optional[typing.Iterable[global___ExponentialHistogramDataPoint]] = ...,
        aggregation_temporality : global___AggregationTemporality.V = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["aggregation_temporality",b"aggregation_temporality","data_points",b"data_points"]) -> None: ...
global___ExponentialHistogram = ExponentialHistogram

class Summary(google.protobuf.message.Message):
    """Summary metric data are used to convey quantile summaries,
    a Prometheus (see: https://prometheus.io/docs/concepts/metric_types/#summary)
    and OpenMetrics (see: https://github.com/OpenObservability/OpenMetrics/blob/4dbf6075567ab43296eed941037c12951faafb92/protos/prometheus.proto#L45)
    data type. These data points cannot always be merged in a meaningful way.
    While they can be useful in some applications, histogram data points are
    recommended for new applications.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    DATA_POINTS_FIELD_NUMBER: builtins.int
    @property
    def data_points(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SummaryDataPoint]: ...
    def __init__(self,
        *,
        data_points : typing.Optional[typing.Iterable[global___SummaryDataPoint]] = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data_points",b"data_points"]) -> None: ...
global___Summary = Summary

class NumberDataPoint(google.protobuf.message.Message):
    """NumberDataPoint is a single data point in a timeseries that describes the
    time-varying scalar value of a metric.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    START_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    AS_DOUBLE_FIELD_NUMBER: builtins.int
    AS_INT_FIELD_NUMBER: builtins.int
    EXEMPLARS_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """The set of key/value pairs that uniquely identify the timeseries from
        where this point belongs. The list may be empty (may contain 0 elements).
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    start_time_unix_nano: builtins.int = ...
    """StartTimeUnixNano is optional but strongly encouraged, see the
    the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    time_unix_nano: builtins.int = ...
    """TimeUnixNano is required, see the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    as_double: builtins.float = ...
    as_int: builtins.int = ...
    @property
    def exemplars(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Exemplar]:
        """(Optional) List of exemplars collected from
        measurements that were used to form the data point
        """
        pass
    flags: builtins.int = ...
    """Flags that apply to this specific data point.  See DataPointFlags
    for the available flags and their meaning.
    """

    def __init__(self,
        *,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        start_time_unix_nano : builtins.int = ...,
        time_unix_nano : builtins.int = ...,
        as_double : builtins.float = ...,
        as_int : builtins.int = ...,
        exemplars : typing.Optional[typing.Iterable[global___Exemplar]] = ...,
        flags : builtins.int = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["as_double",b"as_double","as_int",b"as_int","value",b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["as_double",b"as_double","as_int",b"as_int","attributes",b"attributes","exemplars",b"exemplars","flags",b"flags","start_time_unix_nano",b"start_time_unix_nano","time_unix_nano",b"time_unix_nano","value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["value",b"value"]) -> typing.Optional[typing_extensions.Literal["as_double","as_int"]]: ...
global___NumberDataPoint = NumberDataPoint

class HistogramDataPoint(google.protobuf.message.Message):
    """HistogramDataPoint is a single data point in a timeseries that describes the
    time-varying values of a Histogram. A Histogram contains summary statistics
    for a population of values, it may optionally contain the distribution of
    those values across a set of buckets.

    If the histogram contains the distribution of values, then both
    "explicit_bounds" and "bucket counts" fields must be defined.
    If the histogram does not contain the distribution of values, then both
    "explicit_bounds" and "bucket_counts" must be omitted and only "count" and
    "sum" are known.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    START_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    COUNT_FIELD_NUMBER: builtins.int
    SUM_FIELD_NUMBER: builtins.int
    BUCKET_COUNTS_FIELD_NUMBER: builtins.int
    EXPLICIT_BOUNDS_FIELD_NUMBER: builtins.int
    EXEMPLARS_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    MIN_FIELD_NUMBER: builtins.int
    MAX_FIELD_NUMBER: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """The set of key/value pairs that uniquely identify the timeseries from
        where this point belongs. The list may be empty (may contain 0 elements).
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    start_time_unix_nano: builtins.int = ...
    """StartTimeUnixNano is optional but strongly encouraged, see the
    the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    time_unix_nano: builtins.int = ...
    """TimeUnixNano is required, see the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    count: builtins.int = ...
    """count is the number of values in the population. Must be non-negative. This
    value must be equal to the sum of the "count" fields in buckets if a
    histogram is provided.
    """

    sum: builtins.float = ...
    """sum of the values in the population. If count is zero then this field
    must be zero.

    Note: Sum should only be filled out when measuring non-negative discrete
    events, and is assumed to be monotonic over the values of these events.
    Negative events *can* be recorded, but sum should not be filled out when
    doing so.  This is specifically to enforce compatibility w/ OpenMetrics,
    see: https://github.com/OpenObservability/OpenMetrics/blob/main/specification/OpenMetrics.md#histogram
    """

    @property
    def bucket_counts(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """bucket_counts is an optional field contains the count values of histogram
        for each bucket.

        The sum of the bucket_counts must equal the value in the count field.

        The number of elements in bucket_counts array must be by one greater than
        the number of elements in explicit_bounds array.
        """
        pass
    @property
    def explicit_bounds(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """explicit_bounds specifies buckets with explicitly defined bounds for values.

        The boundaries for bucket at index i are:

        (-infinity, explicit_bounds[i]] for i == 0
        (explicit_bounds[i-1], explicit_bounds[i]] for 0 < i < size(explicit_bounds)
        (explicit_bounds[i-1], +infinity) for i == size(explicit_bounds)

        The values in the explicit_bounds array must be strictly increasing.

        Histogram buckets are inclusive of their upper boundary, except the last
        bucket where the boundary is at infinity. This format is intentionally
        compatible with the OpenMetrics histogram definition.
        """
        pass
    @property
    def exemplars(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Exemplar]:
        """(Optional) List of exemplars collected from
        measurements that were used to form the data point
        """
        pass
    flags: builtins.int = ...
    """Flags that apply to this specific data point.  See DataPointFlags
    for the available flags and their meaning.
    """

    min: builtins.float = ...
    """min is the minimum value over (start_time, end_time]."""

    max: builtins.float = ...
    """max is the maximum value over (start_time, end_time]."""

    def __init__(self,
        *,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        start_time_unix_nano : builtins.int = ...,
        time_unix_nano : builtins.int = ...,
        count : builtins.int = ...,
        sum : builtins.float = ...,
        bucket_counts : typing.Optional[typing.Iterable[builtins.int]] = ...,
        explicit_bounds : typing.Optional[typing.Iterable[builtins.float]] = ...,
        exemplars : typing.Optional[typing.Iterable[global___Exemplar]] = ...,
        flags : builtins.int = ...,
        min : builtins.float = ...,
        max : builtins.float = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_max",b"_max","_min",b"_min","_sum",b"_sum","max",b"max","min",b"min","sum",b"sum"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_max",b"_max","_min",b"_min","_sum",b"_sum","attributes",b"attributes","bucket_counts",b"bucket_counts","count",b"count","exemplars",b"exemplars","explicit_bounds",b"explicit_bounds","flags",b"flags","max",b"max","min",b"min","start_time_unix_nano",b"start_time_unix_nano","sum",b"sum","time_unix_nano",b"time_unix_nano"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_max",b"_max"]) -> typing.Optional[typing_extensions.Literal["max"]]: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_min",b"_min"]) -> typing.Optional[typing_extensions.Literal["min"]]: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_sum",b"_sum"]) -> typing.Optional[typing_extensions.Literal["sum"]]: ...
global___HistogramDataPoint = HistogramDataPoint

class ExponentialHistogramDataPoint(google.protobuf.message.Message):
    """ExponentialHistogramDataPoint is a single data point in a timeseries that describes the
    time-varying values of a ExponentialHistogram of double values. A ExponentialHistogram contains
    summary statistics for a population of values, it may optionally contain the
    distribution of those values across a set of buckets.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    class Buckets(google.protobuf.message.Message):
        """Buckets are a set of bucket counts, encoded in a contiguous array
        of counts.
        """
        DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
        OFFSET_FIELD_NUMBER: builtins.int
        BUCKET_COUNTS_FIELD_NUMBER: builtins.int
        offset: builtins.int = ...
        """Offset is the bucket index of the first entry in the bucket_counts array.

        Note: This uses a varint encoding as a simple form of compression.
        """

        @property
        def bucket_counts(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
            """bucket_counts is an array of count values, where bucket_counts[i] carries
            the count of the bucket at index (offset+i). bucket_counts[i] is the count
            of values greater than base^(offset+i) and less than or equal to
            base^(offset+i+1).

            Note: By contrast, the explicit HistogramDataPoint uses
            fixed64.  This field is expected to have many buckets,
            especially zeros, so uint64 has been selected to ensure
            varint encoding.
            """
            pass
        def __init__(self,
            *,
            offset : builtins.int = ...,
            bucket_counts : typing.Optional[typing.Iterable[builtins.int]] = ...,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["bucket_counts",b"bucket_counts","offset",b"offset"]) -> None: ...

    ATTRIBUTES_FIELD_NUMBER: builtins.int
    START_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    COUNT_FIELD_NUMBER: builtins.int
    SUM_FIELD_NUMBER: builtins.int
    SCALE_FIELD_NUMBER: builtins.int
    ZERO_COUNT_FIELD_NUMBER: builtins.int
    POSITIVE_FIELD_NUMBER: builtins.int
    NEGATIVE_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    EXEMPLARS_FIELD_NUMBER: builtins.int
    MIN_FIELD_NUMBER: builtins.int
    MAX_FIELD_NUMBER: builtins.int
    ZERO_THRESHOLD_FIELD_NUMBER: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """The set of key/value pairs that uniquely identify the timeseries from
        where this point belongs. The list may be empty (may contain 0 elements).
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    start_time_unix_nano: builtins.int = ...
    """StartTimeUnixNano is optional but strongly encouraged, see the
    the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    time_unix_nano: builtins.int = ...
    """TimeUnixNano is required, see the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    count: builtins.int = ...
    """count is the number of values in the population. Must be
    non-negative. This value must be equal to the sum of the "bucket_counts"
    values in the positive and negative Buckets plus the "zero_count" field.
    """

    sum: builtins.float = ...
    """sum of the values in the population. If count is zero then this field
    must be zero.

    Note: Sum should only be filled out when measuring non-negative discrete
    events, and is assumed to be monotonic over the values of these events.
    Negative events *can* be recorded, but sum should not be filled out when
    doing so.  This is specifically to enforce compatibility w/ OpenMetrics,
    see: https://github.com/OpenObservability/OpenMetrics/blob/main/specification/OpenMetrics.md#histogram
    """

    scale: builtins.int = ...
    """scale describes the resolution of the histogram.  Boundaries are
    located at powers of the base, where:

      base = (2^(2^-scale))

    The histogram bucket identified by `index`, a signed integer,
    contains values that are greater than (base^index) and
    less than or equal to (base^(index+1)).

    The positive and negative ranges of the histogram are expressed
    separately.  Negative values are mapped by their absolute value
    into the negative range using the same scale as the positive range.

    scale is not restricted by the protocol, as the permissible
    values depend on the range of the data.
    """

    zero_count: builtins.int = ...
    """zero_count is the count of values that are either exactly zero or
    within the region considered zero by the instrumentation at the
    tolerated degree of precision.  This bucket stores values that
    cannot be expressed using the standard exponential formula as
    well as values that have been rounded to zero.

    Implementations MAY consider the zero bucket to have probability
    mass equal to (zero_count / count).
    """

    @property
    def positive(self) -> global___ExponentialHistogramDataPoint.Buckets:
        """positive carries the positive range of exponential bucket counts."""
        pass
    @property
    def negative(self) -> global___ExponentialHistogramDataPoint.Buckets:
        """negative carries the negative range of exponential bucket counts."""
        pass
    flags: builtins.int = ...
    """Flags that apply to this specific data point.  See DataPointFlags
    for the available flags and their meaning.
    """

    @property
    def exemplars(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Exemplar]:
        """(Optional) List of exemplars collected from
        measurements that were used to form the data point
        """
        pass
    min: builtins.float = ...
    """min is the minimum value over (start_time, end_time]."""

    max: builtins.float = ...
    """max is the maximum value over (start_time, end_time]."""

    zero_threshold: builtins.float = ...
    """ZeroThreshold may be optionally set to convey the width of the zero
    region. Where the zero region is defined as the closed interval
    [-ZeroThreshold, ZeroThreshold].
    When ZeroThreshold is 0, zero count bucket stores values that cannot be
    expressed using the standard exponential formula as well as values that
    have been rounded to zero.
    """

    def __init__(self,
        *,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        start_time_unix_nano : builtins.int = ...,
        time_unix_nano : builtins.int = ...,
        count : builtins.int = ...,
        sum : builtins.float = ...,
        scale : builtins.int = ...,
        zero_count : builtins.int = ...,
        positive : typing.Optional[global___ExponentialHistogramDataPoint.Buckets] = ...,
        negative : typing.Optional[global___ExponentialHistogramDataPoint.Buckets] = ...,
        flags : builtins.int = ...,
        exemplars : typing.Optional[typing.Iterable[global___Exemplar]] = ...,
        min : builtins.float = ...,
        max : builtins.float = ...,
        zero_threshold : builtins.float = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_max",b"_max","_min",b"_min","_sum",b"_sum","max",b"max","min",b"min","negative",b"negative","positive",b"positive","sum",b"sum"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_max",b"_max","_min",b"_min","_sum",b"_sum","attributes",b"attributes","count",b"count","exemplars",b"exemplars","flags",b"flags","max",b"max","min",b"min","negative",b"negative","positive",b"positive","scale",b"scale","start_time_unix_nano",b"start_time_unix_nano","sum",b"sum","time_unix_nano",b"time_unix_nano","zero_count",b"zero_count","zero_threshold",b"zero_threshold"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_max",b"_max"]) -> typing.Optional[typing_extensions.Literal["max"]]: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_min",b"_min"]) -> typing.Optional[typing_extensions.Literal["min"]]: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_sum",b"_sum"]) -> typing.Optional[typing_extensions.Literal["sum"]]: ...
global___ExponentialHistogramDataPoint = ExponentialHistogramDataPoint

class SummaryDataPoint(google.protobuf.message.Message):
    """SummaryDataPoint is a single data point in a timeseries that describes the
    time-varying values of a Summary metric.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    class ValueAtQuantile(google.protobuf.message.Message):
        """Represents the value at a given quantile of a distribution.

        To record Min and Max values following conventions are used:
        - The 1.0 quantile is equivalent to the maximum value observed.
        - The 0.0 quantile is equivalent to the minimum value observed.

        See the following issue for more context:
        https://github.com/open-telemetry/opentelemetry-proto/issues/125
        """
        DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
        QUANTILE_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        quantile: builtins.float = ...
        """The quantile of a distribution. Must be in the interval
        [0.0, 1.0].
        """

        value: builtins.float = ...
        """The value at the given quantile of a distribution.

        Quantile values must NOT be negative.
        """

        def __init__(self,
            *,
            quantile : builtins.float = ...,
            value : builtins.float = ...,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["quantile",b"quantile","value",b"value"]) -> None: ...

    ATTRIBUTES_FIELD_NUMBER: builtins.int
    START_TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    COUNT_FIELD_NUMBER: builtins.int
    SUM_FIELD_NUMBER: builtins.int
    QUANTILE_VALUES_FIELD_NUMBER: builtins.int
    FLAGS_FIELD_NUMBER: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """The set of key/value pairs that uniquely identify the timeseries from
        where this point belongs. The list may be empty (may contain 0 elements).
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
        pass
    start_time_unix_nano: builtins.int = ...
    """StartTimeUnixNano is optional but strongly encouraged, see the
    the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    time_unix_nano: builtins.int = ...
    """TimeUnixNano is required, see the detailed comments above Metric.

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    count: builtins.int = ...
    """count is the number of values in the population. Must be non-negative."""

    sum: builtins.float = ...
    """sum of the values in the population. If count is zero then this field
    must be zero.

    Note: Sum should only be filled out when measuring non-negative discrete
    events, and is assumed to be monotonic over the values of these events.
    Negative events *can* be recorded, but sum should not be filled out when
    doing so.  This is specifically to enforce compatibility w/ OpenMetrics,
    see: https://github.com/OpenObservability/OpenMetrics/blob/main/specification/OpenMetrics.md#summary
    """

    @property
    def quantile_values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SummaryDataPoint.ValueAtQuantile]:
        """(Optional) list of values at different quantiles of the distribution calculated
        from the current snapshot. The quantiles must be strictly increasing.
        """
        pass
    flags: builtins.int = ...
    """Flags that apply to this specific data point.  See DataPointFlags
    for the available flags and their meaning.
    """

    def __init__(self,
        *,
        attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        start_time_unix_nano : builtins.int = ...,
        time_unix_nano : builtins.int = ...,
        count : builtins.int = ...,
        sum : builtins.float = ...,
        quantile_values : typing.Optional[typing.Iterable[global___SummaryDataPoint.ValueAtQuantile]] = ...,
        flags : builtins.int = ...,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attributes",b"attributes","count",b"count","flags",b"flags","quantile_values",b"quantile_values","start_time_unix_nano",b"start_time_unix_nano","sum",b"sum","time_unix_nano",b"time_unix_nano"]) -> None: ...
global___SummaryDataPoint = SummaryDataPoint

class Exemplar(google.protobuf.message.Message):
    """A representation of an exemplar, which is a sample input measurement.
    Exemplars also hold information about the environment when the measurement
    was recorded, for example the span and trace ID of the active span when the
    exemplar was recorded.
    """
    DESCRIPTOR: google.protobuf.descriptor.Descriptor = ...
    FILTERED_ATTRIBUTES_FIELD_NUMBER: builtins.int
    TIME_UNIX_NANO_FIELD_NUMBER: builtins.int
    AS_DOUBLE_FIELD_NUMBER: builtins.int
    AS_INT_FIELD_NUMBER: builtins.int
    SPAN_ID_FIELD_NUMBER: builtins.int
    TRACE_ID_FIELD_NUMBER: builtins.int
    @property
    def filtered_attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """The set of key/value pairs that were filtered out by the aggregator, but
        recorded alongside the original measurement. Only key/value pairs that were
        filtered out by the aggregator should be included
        """
        pass
    time_unix_nano: builtins.int = ...
    """time_unix_nano is the exact time when this exemplar was recorded

    Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January
    1970.
    """

    as_double: builtins.float = ...
    as_int: builtins.int = ...
    span_id: builtins.bytes = ...
    """(Optional) Span ID of the exemplar trace.
    span_id may be missing if the measurement is not recorded inside a trace
    or if the trace is not sampled.
    """

    trace_id: builtins.bytes = ...
    """(Optional) Trace ID of the exemplar trace.
    trace_id may be missing if the measurement is not recorded inside a trace
    or if the trace is not sampled.
    """

    def __init__(self,
        *,
        filtered_attributes : typing.Optional[typing.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue]] = ...,
        time_unix_nano : builtins.int = ...,
        as_double : builtins.float = ...,
        as_int : builtins.int = ...,
        span_id : builtins.bytes = ...,
        trace_id : builtins.bytes = ...,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["as_double",b"as_double","as_int",b"as_int","value",b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["as_double",b"as_double","as_int",b"as_int","filtered_attributes",b"filtered_attributes","span_id",b"span_id","time_unix_nano",b"time_unix_nano","trace_id",b"trace_id","value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["value",b"value"]) -> typing.Optional[typing_extensions.Literal["as_double","as_int"]]: ...
global___Exemplar = Exemplar
