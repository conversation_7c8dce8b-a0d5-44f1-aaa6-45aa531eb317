# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from enum import Enum
from typing import Final

VCS_REPOSITORY_CHANGE_ID: Final = "vcs.repository.change.id"
"""
The ID of the change (pull request/merge request) if applicable. This is usually a unique (within repository) identifier generated by the VCS system.
"""

VCS_REPOSITORY_CHANGE_TITLE: Final = "vcs.repository.change.title"
"""
The human readable title of the change (pull request/merge request). This title is often a brief summary of the change and may get merged in to a ref as the commit summary.
"""

VCS_REPOSITORY_REF_NAME: Final = "vcs.repository.ref.name"
"""
The name of the [reference](https://git-scm.com/docs/gitglossary#def_ref) such as **branch** or **tag** in the repository.
"""

VCS_REPOSITORY_REF_REVISION: Final = "vcs.repository.ref.revision"
"""
The revision, literally [revised version](https://www.merriam-webster.com/dictionary/revision), The revision most often refers to a commit object in Git, or a revision number in SVN.
Note: The revision can be a full [hash value (see glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf),
of the recorded change to a ref within a repository pointing to a
commit [commit](https://git-scm.com/docs/git-commit) object. It does
not necessarily have to be a hash; it can simply define a
[revision number](https://svnbook.red-bean.com/en/1.7/svn.tour.revs.specifiers.html)
which is an integer that is monotonically increasing. In cases where
it is identical to the `ref.name`, it SHOULD still be included. It is
up to the implementer to decide which value to set as the revision
based on the VCS system and situational context.
"""

VCS_REPOSITORY_REF_TYPE: Final = "vcs.repository.ref.type"
"""
The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.
"""

VCS_REPOSITORY_URL_FULL: Final = "vcs.repository.url.full"
"""
The [URL](https://en.wikipedia.org/wiki/URL) of the repository providing the complete address in order to locate and identify the repository.
"""


class VcsRepositoryRefTypeValues(Enum):
    BRANCH = "branch"
    """[branch](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddefbranchabranch)."""
    TAG = "tag"
    """[tag](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddeftagatag)."""
